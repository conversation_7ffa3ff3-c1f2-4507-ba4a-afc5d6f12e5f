# MassGen Configuration: Gemini with Streamable HTTP MCP Integration
# This configuration tests the streamable-http transport for MCP servers
# Prerequisites:
# 1. Start the test MCP server: python massgen/tests/test_http_mcp_server.py
# 2. Verify server is running at http://localhost:8000/mcp
# 3. Run: uv run python -m massgen.cli --config configs/gemini_streamable_http_test.yaml "check for my events this week and birthdays"

agents:
  - id: "gemini_streamable_http_test"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      mcp_servers:
        - name: "streamable_http_server"
          type: "streamable-http"
          url: "http://localhost:8000/mcp"
          # Security configuration for testing environment
          security:
            level: "permissive"  # Allow more flexibility for testing
            allow_localhost: true  # Required for localhost:8000 connection
            allow_private_ips: true  # Allow private IP ranges for testing
    system_message: |
      You are testing the MCP integration with Streamable HTTP transport using the "streamable_http_server".
      
      The Streamable HTTP transport enables multiple client connections and supports server-to-client streaming

ui:
  display_type: "rich_terminal"
  logging_enabled: true
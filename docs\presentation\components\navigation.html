<!-- Navigation -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">24</span>
    </div>

    <div class="slide-nav">
        <div class="nav-grid" id="nav-grid">
            <!-- Dots will be generated by JavaScript -->
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" id="prev-btn" onclick="changeSlide(-1)" aria-label="Go to previous slide">← Previous</button>
        <button class="nav-btn" id="next-btn" onclick="changeSlide(1)" aria-label="Go to next slide">Next →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        // Slide titles for navigation
        const slideTitles = [
            "Title - M2L Summer School",
            "Problem - Single-Agent Limitation", 
            "Solution - Multi-Agent Collaboration",
            "AG2 - Research Foundation",
            "Evidence - Performance Gains",
            "Architecture - System Design",
            "Features & Capabilities",
            "Tech - Async Streaming",
            "Tech - Backend Challenges", 
            "Tech - Binary Decision Framework",
            "Context Sharing - Challenge",
            "Context Sharing - Solution v0.0.12",
            "Context Sharing - In Action",
            "Benchmarking - Multi-Domain Performance",
            "Case Study - Success Through Collaboration", 
            "Case Study - When Coordination Fails",
            "Coordination Psychology - Voting Behavior",
            "Evolution - v0.0.1 to v0.0.16",
            "Early Adopters - Community Growth",
            "Demo - Live Examples",
            "Research & Learning Applications",
            "Getting Started - 60 Seconds",
            "Vision - Exponential Intelligence",
            "Call to Action - Join Revolution"
        ];

        document.getElementById('total-slides').textContent = totalSlides;

        // Generate navigation dots
        const navGrid = document.getElementById('nav-grid');
        for (let i = 0; i < totalSlides; i++) {
            const dot = document.createElement('div');
            dot.className = 'nav-dot';
            dot.title = `Slide ${i + 1}: ${slideTitles[i]}`;
            dot.onclick = () => showSlide(i);
            navGrid.appendChild(dot);
        }

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            
            document.getElementById('current-slide').textContent = currentSlide + 1;
            
            // Update navigation dots
            document.querySelectorAll('.nav-dot').forEach((dot, index) => {
                dot.classList.toggle('active', index === currentSlide);
            });
            
            // Update navigation buttons
            document.getElementById('prev-btn').disabled = currentSlide === 0;
            document.getElementById('next-btn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            if (direction === 1 && currentSlide < totalSlides - 1) {
                showSlide(currentSlide + 1);
            } else if (direction === -1 && currentSlide > 0) {
                showSlide(currentSlide - 1);
            }
        }

        // Enhanced keyboard navigation
        document.addEventListener('keydown', (e) => {
            // Prevent default behavior if we handle the key
            const handledKeys = ['ArrowRight', 'ArrowLeft', ' ', 'Home', 'End'];
            const numberKeys = e.key >= '1' && e.key <= '9';
            
            if (handledKeys.includes(e.key) || numberKeys) {
                e.preventDefault();
            }
            
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    changeSlide(1);
                    break;
                case 'ArrowLeft':
                    changeSlide(-1);
                    break;
                case 'Home':
                    showSlide(0);
                    break;
                case 'End':
                    showSlide(totalSlides - 1);
                    break;
                default:
                    if (numberKeys) {
                        const slideNum = parseInt(e.key) - 1;
                        if (slideNum < totalSlides) {
                            showSlide(slideNum);
                        }
                    }
            }
        });

        // Initialize
        showSlide(0);
    </script>
</body>
</html>
# MassGen Configuration
# Usage:
# uv run python -m massgen.cli --config configs/gemini_mcp_test.yaml "Test the MCP tools by calling mcp_echo with text 'Hello massgen' and add_numbers with 46 and 52"
agents:
  - id: "gemini_mcp_test"
    backend:
      type: "openai"
      model: "gpt-5-nano"
      mcp_servers:
        - name: "test_server"
          type: "stdio"
          command: "python"
          args: ["-u", "-m", "massgen.tests.mcp_test_server"]
    system_message: |
      You are testing the MCP integration with Google Gemini.

      MCP tools from the "test_server" will be available via sessions and auto-called by the system when needed.
      Do not output function-call syntax. Simply perform the tasks and present clear, concise results.

ui:
  display_type: "rich_terminal"
  logging_enabled: true

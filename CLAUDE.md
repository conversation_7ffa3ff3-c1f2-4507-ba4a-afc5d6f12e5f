# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Testing
```bash
# Run all tests
uv run pytest massgen/tests/

# Run specific test file
uv run pytest massgen/tests/test_claude_backend.py

# Run with coverage
uv run pytest --cov=massgen massgen/tests/

# Run tests with debug output
uv run pytest -v massgen/tests/

# Run integration tests only
uv run pytest -m integration massgen/tests/

# Run unit tests only
uv run pytest -m unit massgen/tests/
```

### Code Quality
```bash
# Format code with black
uv run black massgen/

# Sort imports with isort
uv run isort massgen/

# Type checking with mypy
uv run mypy massgen/

# Lint with flake8
uv run flake8 massgen/

# Run all quality checks
uv run black massgen/ && uv run isort massgen/ && uv run mypy massgen/
```

### Running MassGen
```bash
# Single agent with specific model
uv run python -m massgen.cli --model claude-3-5-sonnet-latest "your question"

# Multi-agent from config
uv run python -m massgen.cli --config massgen/configs/three_agents_default.yaml "your question"

# Debug mode
uv run python -m massgen.cli --model claude-3-5-sonnet-latest --debug "your question"

# Interactive mode
uv run python -m massgen.cli --config massgen/configs/three_agents_default.yaml
```

## Architecture Overview

MassGen is a multi-agent scaling system that coordinates multiple AI agents to solve complex tasks through parallel processing and real-time collaboration. The system follows a modular architecture with clear separation of concerns.

### Core Components

#### 1. Orchestrator (`massgen/orchestrator.py`)
- **Purpose**: Central coordination hub that manages multiple agents
- **Key Responsibilities**:
  - Agent lifecycle management and coordination
  - Consensus building through voting mechanisms
  - Real-time collaboration and message routing
  - Convergence detection and decision making
- **Architecture**: Uses asyncio for non-blocking agent coordination
- **State Management**: Maintains agent states, voting records, and convergence metrics

#### 2. Backend System (`massgen/backend/`)
The backend system supports both stateless and stateful LLM providers:

- **Base Backend** (`base.py`): Abstract interface for all LLM providers
- **Stateless Backends**: OpenAI, Gemini, Grok, etc. (no conversation state)
- **Stateful Backends**: Claude Code CLI (maintains conversation context)
- **Stream Processing**: Unified streaming via `StreamChunk` dataclass

#### 3. MCP Tools Framework (`massgen/mcp_tools/`)
**Model Context Protocol** integration for external tool access:

- **Client** (`client.py`): Multi-server MCP client with stdio and HTTP transport
- **Backend Utils** (`backend_utils.py`): Shared utilities for all backends
- **Circuit Breaker** (`circuit_breaker.py`): Fault tolerance and reliability
- **Security** (`security.py`): Command sanitization and validation
- **Config Validator** (`config_validator.py`): MCP configuration validation

#### 4. Frontend Display System (`massgen/frontend/`)
Real-time visualization of agent collaboration:

- **Rich Terminal Display**: Multi-region layout with live status updates
- **Terminal Display**: Standard terminal output
- **Simple Display**: Plain text output without formatting

### Key Architectural Patterns

#### Agent Coordination Flow
```mermaid
graph TB
    O[Orchestrator] --> A1[Agent 1]
    O --> A2[Agent 2]
    O --> A3[Agent 3]
    A1 --> H[Shared Hub]
    A2 --> H
    A3 --> H
    H --> O
```

#### Backend Architecture
- **Stateless Backends**: Complete context sent with each request
- **Stateful Backends**: Context maintained internally across requests
- **MCP Integration**: External tool access via Model Context Protocol

#### MCP Server Integration
- **Transport Types**: stdio (process-based) and streamable-http (web-based)
- **Tool Discovery**: Automatic tool detection with name prefixing
- **Fault Tolerance**: Circuit breaker patterns for reliability
- **Security**: Command sanitization and credential management

### Configuration System

#### YAML Configuration Structure
```yaml
# Single agent
agent:
  id: "agent_name"
  backend:
    type: "claude" | "gemini" | "openai" | "claude_code" | "grok" | "azure_openai"
    model: "model_name"
  system_message: "Custom system prompt"

# Multi-agent
agents:
  - id: "agent1"
    backend:
      type: "claude"
      model: "claude-3-5-sonnet-latest"
  - id: "agent2"
    backend:
      type: "gemini"
      model: "gemini-2.5-flash"

# MCP configuration (for supported backends)
mcp_servers:
  weather:
    type: "stdio"
    command: "npx"
    args: ["-y", "@fak111/weather-mcp"]
```

### Backend Implementation Patterns

#### Stateless Backend Example
```python
class OpenAIBackend(LLMBackend):
    async def stream_with_tools(self, messages, tools):
        # Send complete context each time
        response = await self.client.chat.completions.create(
            messages=messages,
            tools=tools
        )
        return self._stream_response(response)
```

#### Stateful Backend Example
```python
class ClaudeCodeBackend(LLMBackend):
    async def stream_with_tools(self, messages, tools):
        # Context maintained internally
        response = await self.claude_client.send_message(
            messages[-1]["content"]  # Only current message
        )
        return self._stream_response(response)
```

### MCP Integration Architecture

#### MCP Tool Processing
1. **Server Discovery**: Parse MCP server configurations
2. **Client Setup**: Establish connections to multiple MCP servers
3. **Tool Registration**: Auto-discover tools with name prefixing
4. **Execution**: Route tool calls to appropriate servers
5. **Error Handling**: Circuit breaker patterns for fault tolerance

#### Key MCP Classes
- **MultiMCPClient**: Manages multiple MCP server connections
- **MCPResourceManager**: Handles MCP client lifecycle and tool conversion
- **MCPCircuitBreakerManager**: Manages circuit breaker state and event recording
- **MCPConfigHelper**: Builds MCP configurations with validation

### Logging and Debugging

#### Logging Structure
```
massgen_logs/
├── log_{timestamp}/
│   ├── agent_outputs/          # Raw agent outputs
│   ├── agent_id/               # Per-agent answer versions
│   ├── final_workspace/        # Final presentations
│   └── massgen.log            # Main system log
```

#### Debug Mode
```bash
# Enable debug logging
uv run python -m massgen.cli --debug "your question"

# Debug logs include:
# - Orchestrator activities and decisions
# - Agent message routing and processing
# - Backend operations and tool calls
# - MCP server communications
```

### Development Guidelines

#### Backend Implementation
1. **Inherit from LLMBackend**: Use `massgen/backend/base.py` as base
2. **Implement stream_with_tools**: Required method for all backends
3. **Handle StreamChunk types**: Support content, tool_calls, reasoning, etc.
4. **MCP Integration**: Use MCPResourceManager for MCP support
5. **Error Handling**: Implement robust error recovery patterns

#### MCP Development
1. **Use Package Imports**: Import from `massgen.mcp_tools` not internal modules
2. **Circuit Breakers**: Always use MCPCircuitBreakerManager for reliability
3. **Security**: Sanitize all commands and validate configurations
4. **Tool Discovery**: Leverage automatic tool discovery mechanisms

#### Configuration Management
1. **YAML Structure**: Follow established configuration patterns
2. **Backend Types**: Use supported backend type names
3. **MCP Servers**: Configure via mcp_servers section
4. **Validation**: Use MCPConfigHelper for configuration validation

### Testing Strategy

#### Test Categories
- **Unit Tests**: Individual component testing
- **Integration Tests**: Backend and orchestrator testing
- **MCP Tests**: MCP server integration testing
- **End-to-End Tests**: Complete workflow testing

#### Test Files
- `test_claude_backend.py`: Claude API backend tests
- `test_claude_code.py`: Claude Code CLI backend tests
- `test_backend_event_loop_all.py`: Backend event loop tests
- `test_v3_*.py`: Multi-agent orchestration tests
- `test_http_mcp_server.py`: MCP HTTP server tests

### Important Notes

#### Environment Setup
- Copy `.env.example` to `.env` and add API keys
- Use `uv` for dependency management
- Python 3.10+ required

#### Backend Support
- **Full MCP Support**: Claude Code, Gemini
- **Basic Tool Support**: OpenAI, Claude API
- **Limited Tool Support**: Grok (web search only)
- **No Tool Support**: Azure OpenAI, Z AI

#### Development Status
- **Active Development**: Expect breaking changes
- **Focus Areas**: MCP integration, agent collaboration, performance
- **Version**: v0.0.15+ with MCP framework
- **Roadmap**: See ROADMAP_v0.0.16.md for upcoming features